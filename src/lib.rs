#![deny(clippy::all)]

use napi::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>};
use solana_entry::entry::Entry;

#[macro_use]
extern crate napi_derive;

mod types;
use types::ParsedEntry;

#[napi]
pub fn decode_entries(bytes: <PERSON><PERSON><PERSON><PERSON><PERSON>) -> Result<ParsedEntry> {
  let entries: Vec<Entry> = bincode::deserialize(&bytes.into_value()?)
    .map_err(|e| Error::new(Status::GenericFailure, e.to_string()))?;

  Ok(ParsedEntry { entries })
}
