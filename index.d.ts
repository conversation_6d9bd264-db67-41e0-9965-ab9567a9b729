/* tslint:disable */
/* eslint-disable */

/* auto-generated by NAPI-RS */

export interface Entry {
  numHashes: number
  hash: Array<number>
  transactions: Array<VersionedTransaction>
}
export interface VersionedTransaction {
  signatures: Array<Array<number>>
  message: string
}
export interface ParsedEntry {
  entries: Array<Entry>
}
export declare function decodeEntries(bytes: Buffer): ParsedEntry
